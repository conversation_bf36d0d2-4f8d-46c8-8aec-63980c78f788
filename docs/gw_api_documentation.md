# GW.py API 接口技术文档

## 概述

`gw.py` 是 Intellix-DS-Agent 项目的核心网关服务文件，基于 FastAPI 框架实现。该文件定义了数据科学智能助手的主要业务接口，包括聊天对话、用户管理、会话管理、反馈系统以及自然语言转SQL等功能模块。

## 技术栈

- **Web框架**: FastAPI
- **数据库**: MySQL (通过 Peewee ORM)
- **搜索引擎**: Elasticsearch
- **分布式计算**: Ray
- **异步处理**: asyncio
- **数据验证**: Pydantic
- **日志系统**: 自定义 Trace Logger

## 全局变量与配置

```python
# 全局MySQL连接池
mysql_pool: MysqlPool = None

# FastAPI应用实例
app = FastAPI(lifespan=lifespan)
```

## 接口详细说明

### 1. validation_exception_handler

**功能**: 全局参数验证异常处理器

```python
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError)
```

**参数**:
- `request`: FastAPI 请求对象
- `exc`: 请求验证异常对象

**返回值**:
- JSONResponse: 统一格式的错误响应

**说明**:
- 处理所有请求参数验证失败的异常
- 返回状态码200，但包含错误信息
- 使用 `gw_error_json` 函数生成标准错误格式

### 2. chat

**功能**: 主聊天接口，处理用户对话请求

```python
@app.post("/chat")
async def chat(chat_config: ChatConfig, request: Request, response: Response, background_tasks: BackgroundTasks)
```

**参数**:
- `chat_config`: ChatConfig 对象，包含对话配置
  - `SessionId`: 会话ID
  - `Question`: 用户问题
  - `Model`: 使用的模型（可选）
  - `DeepThinking`: 是否深度思考（默认True）
  - `DataSourceIds`: 数据源ID列表
  - `KnowledgeBaseIds`: 知识库ID列表
  - `AgentType`: 代理类型
  - `Context`: 上下文信息
  - `OldRecordId`: 历史记录ID
- `request`: FastAPI 请求对象
- `response`: FastAPI 响应对象
- `background_tasks`: 后台任务管理器

**返回值**:
- 异步返回对话处理结果

**说明**:
- 使用 ChatManager 处理对话逻辑
- 支持后台任务处理
- 集成了多种数据源和知识库

### 3. continue_chat

**功能**: 继续对话接口，基于历史记录继续对话

```python
@app.post("/continue")
async def continue_chat(continue_config: ContinueConfig, request: Request, response: Response)
```

**参数**:
- `continue_config`: ContinueConfig 对象
  - `SessionId`: 会话ID
  - `RecordId`: 历史记录ID（可选）
- `request`: FastAPI 请求对象
- `response`: FastAPI 响应对象

**返回值**:
- 异步返回继续对话的结果

**说明**:
- 基于指定的会话和记录继续对话
- 保持对话上下文的连续性

### 4. chat_stop_stream

**功能**: 停止聊天流接口

```python
@app.post("/chat/stop_stream")
def chat_stop_stream(stop_chat_config: StopChatConfig, request: Request, response: Response)
```

**参数**:
- `stop_chat_config`: StopChatConfig 对象
  - `SessionId`: 会话ID
- `request`: FastAPI 请求对象
- `response`: FastAPI 响应对象

**返回值**:
- StopChatResponse 对象
  - `Status`: HTTP状态码
  - `Message`: 响应消息

**异常处理**:
- 成功时返回200状态码
- 失败时返回500状态码
- 捕获所有异常并记录日志

**说明**:
- 通过 AgentManager 停止指定的Agent
- 使用 `sub_account_uin` 和 `session_id` 构造唯一的actor名称
- 支持强制停止正在进行的对话

### 5. update_user_info

**功能**: 用户信息管理接口，支持查询、创建、修改、删除操作

```python
@app.post("/update_user_info")
def update_user(update_user_info: UpdateUserInfoConfig, request: Request, response: Response)
```

**参数**:
- `update_user_info`: UpdateUserInfoConfig 对象
  - `JupyterHost`: Jupyter主机地址
  - `Operate`: 操作类型（query/create/modify/delete）
- `request`: FastAPI 请求对象
- `response`: FastAPI 响应对象

**返回值**:
- 查询操作：返回用户详细信息
- 其他操作：返回操作状态

**操作类型说明**:
- `query`: 查询用户信息
- `create`: 创建用户信息
- `modify`: 修改用户信息
- `delete`: 标记删除用户信息（软删除）

**异常处理**:
- 用户不存在时返回404状态码
- 操作失败时返回500状态码
- 捕获所有异常并记录日志

**说明**:
- 使用 UserInfoAdapter 进行数据库操作
- 支持软删除机制
- 返回标准化的用户信息格式

### 6. query_user_session_list

**功能**: 查询用户会话列表

```python
@app.post("/query_user_session_list")
def query_user_sessions(query_user_session_list: BaseModel, request: Request, response: Response)
```

**参数**:
- `query_user_session_list`: 基础模型对象
- `request`: FastAPI 请求对象
- `response`: FastAPI 响应对象

**返回值**:
```json
{
  "Status": 200,
  "SessionInfoList": [
    {
      "SessionId": "会话ID",
      "SessionTitle": "会话标题",
      "DbInfo": "数据库信息",
      "CreateTime": "创建时间",
      "UpdateTime": "更新时间"
    }
  ]
}
```

**说明**:
- 获取用户所有未删除的会话
- 使用 UserSessionAdapter 进行数据库查询
- 返回会话的基本信息列表

### 7. query_user_session_detail

**功能**: 查询用户会话详细信息

```python
@app.post("/query_user_session_info")
def query_user_session_detail(query_user_session_info: QueryUserSessionInfoConfig, request: Request, response: Response)
```

**参数**:
- `query_user_session_info`: QueryUserSessionInfoConfig 对象
  - `SessionId`: 会话ID
- `request`: FastAPI 请求对象
- `response`: FastAPI 响应对象

**返回值**:
```json
{
  "Status": 200,
  "SubAccountUin": "用户账号",
  "SessionId": "会话ID",
  "RecordList": [
    {
      "SessionId": "会话ID",
      "SubAccountUin": "用户账号",
      "RecordId": "记录ID",
      "Context": "上下文",
      "Answer": "回答",
      "Question": "问题",
      "FinalSummary": "最终总结",
      "KnowledgeBaseIds": "知识库ID列表",
      "Think": "思考链",
      "ErrorContext": "错误上下文",
      "TaskList": "任务列表(JSON)",
      "DbInfo": "数据库信息(JSON)",
      "CreateTime": "创建时间",
      "UpdateTime": "更新时间",
      "Feedback": "反馈"
    }
  ],
  "RunRecord": "运行记录",
  "RecordCount": 记录数量
}
```

**说明**:
- 先检查会话是否存在
- 从Elasticsearch查询会话记录
- 处理 task_list 和 task_list_dict 的兼容性
- 返回完整的会话历史记录

### 8. delete_data_agent_session

**功能**: 删除用户会话（软删除）

```python
@app.post("/delete_data_agent_session")
def delete_data_agent(delete_data_agent_session: DeleteDataAgentSessionConfig, request: Request, response: Response)
```

**参数**:
- `delete_data_agent_session`: DeleteDataAgentSessionConfig 对象
  - `SessionId`: 会话ID
- `request`: FastAPI 请求对象
- `response`: FastAPI 响应对象

**返回值**:
```json
{
  "Status": 200
}
```

**异常处理**:
- 会话不存在时返回404状态码
- 删除失败时返回500状态码
- 捕获所有异常并记录日志

**说明**:
- 使用软删除机制，标记会话为已删除
- 通过 UserSessionAdapter 进行数据库操作

### 9. add_feedback

**功能**: 添加用户反馈

```python
@app.post("/add_feedback")
def add_feedback(add_feedback_config: AddFeedbackConfig, request: Request, response: Response)
```

**参数**:
- `add_feedback_config`: AddFeedbackConfig 对象
  - `RecordId`: 记录ID
  - `Feedback`: 反馈值（整数）
- `request`: FastAPI 请求对象
- `response`: FastAPI 响应对象

**返回值**:
```json
{
  "Status": 200
}
```

**异常处理**:
- 更新失败时返回500状态码
- 捕获所有异常并记录日志

**说明**:
- 同时更新Elasticsearch和generate_sql表的反馈信息
- 检查是否包含generate_sql工具调用
- 使用专门的trace logger记录反馈操作

### 10. NL2SQLConfig 和 SchemaLinkingConfig

**功能**: 自然语言转SQL相关的数据模型

```python
class NL2SQLConfig(BaseModel):
    """自然语言转SQL接口参数"""
    app_id: str
    sub_account_uin: str
    trace_id: str
    data_engine_name: Optional[str] = None
    db_info: str
    is_sampling: bool = False
    mcp_url: dict
    type: str
    question: str
    record_id: str

class SchemaLinkingConfig(BaseModel):
    """Schema Linking接口参数"""
    app_id: str
    sub_account_uin: str
    trace_id: str
    data_engine_name: Optional[str] = None
    db_info: str
    is_sampling: bool = False
    mcp_url: dict
    type: str
    question: str
    record_id: str
```

**参数说明**:
- `app_id`: 应用ID
- `sub_account_uin`: 用户账号
- `trace_id`: 追踪ID
- `data_engine_name`: 数据引擎名称（可选）
- `db_info`: 数据库信息
- `is_sampling`: 是否采样（默认False）
- `mcp_url`: MCP URL配置
- `type`: 类型
- `question`: 自然语言问题
- `record_id`: 记录ID

### 11. nl2sql

**功能**: 自然语言转SQL接口

```python
@app.post("/nl2sql")
def nl2sql(params: NL2SQLConfig, request: Request, response: Response)
```

**参数**:
- `params`: NL2SQLConfig 对象
- `request`: FastAPI 请求对象
- `response`: FastAPI 响应对象

**返回值**:
```json
{
  "Status": 200,
  "data": "SQL生成结果"
}
```

**异常处理**:
- 参数验证失败时返回422状态码
- SQL生成异常时返回500状态码
- 捕获所有异常并记录日志

**说明**:
- 使用 Pydantic 模型验证参数
- 调用外部 generate_sql 函数处理逻辑
- 返回标准格式的响应结果

### 12. nl2sql_select_tables

**功能**: Schema Linking接口，分析相关问题数据库列

```python
@app.post("/select_tables")
def nl2sql_select_tables(params: SchemaLinkingConfig, request: Request, response: Response)
```

**参数**:
- `params`: SchemaLinkingConfig 对象
- `request`: FastAPI 请求对象
- `response`: FastAPI 响应对象

**返回值**:
```json
{
  "Status": 200,
  "data": "Schema Linking结果"
}
```

**异常处理**:
- 参数验证失败时返回422状态码
- 处理异常时返回500状态码
- 捕获所有异常并记录日志

**说明**:
- 根据自然语言问题分析相关的数据库列
- 调用外部 select_tables 函数处理逻辑
- 支持多种数据库类型

### 13. query_expand

**功能**: 查询扩展接口

```python
@app.post("/query_expand")
def query_expand(query_expand_info: QueryExpandInfoConfig, request: Request, response: Response)
```

**参数**:
- `query_expand_info`: QueryExpandInfoConfig 对象
  - `SessionId`: 会话ID
  - `RecordId`: 记录ID
  - `CellId`: 单元格ID
- `request`: FastAPI 请求对象
- `response`: FastAPI 响应对象

**返回值**:
- Jupyter笔记本相关查询结果

**说明**:
- 使用 JupyterOperator 查询Jupyter记录
- 支持按会话、记录、单元格进行精确查询
- 集成了Jupyter笔记本的查询功能

## 数据模型说明

### 基础配置模型

```python
class BaseUserSessionConfig(BaseModel):
    SessionId: str
```

### 错误处理模型

```python
class ErrorInfo(BaseModel):
    Message: Optional[str] = None
    Code: Optional[str] = None

class CommonResponse(BaseModel):
    Status: int
    Error: Optional[ErrorInfo] = None
```

### 工具函数

```python
def gw_error(status: int, err_code: ErrorCode, err_message: str = None) -> CommonResponse
def gw_error_json(status: int, err_code: ErrorCode, err_message: str = None) -> dict
```

**功能**: 生成标准错误响应

**参数**:
- `status`: HTTP状态码
- `err_code`: 错误代码
- `err_message`: 错误消息（可选）

**返回值**:
- CommonResponse 对象或其JSON字符串

## 异常处理机制

### 全局异常处理

1. **参数验证异常**: `validation_exception_handler`
   - 统一处理 RequestValidationError
   - 返回标准格式的错误响应

2. **业务异常**: 各接口单独处理
   - 使用 try-catch 捕获异常
   - 记录详细的错误日志
   - 返回适当的HTTP状态码

### 错误代码定义

- `ParamError`: 参数错误
- `NotFoundError`: 资源不存在
- `InternalError`: 内部错误
- 其他自定义错误代码

## 日志系统

### Trace Logger

```python
from common.logger.logger import get_trace_logger
```

**功能**: 提供带追踪ID的日志记录

**使用方式**:
```python
logger = get_trace_logger(ctx, "module_name")
logger.info("日志消息")
logger.error("错误消息", exc_info=True)
```

**特点**:
- 自动包含追踪ID
- 支持不同级别的日志
- 异常信息自动记录堆栈

## 数据库操作

### MySQL连接池

```python
mysql_pool: MysqlPool = None
```

**说明**:
- 使用连接池管理数据库连接
- 支持事务操作
- 通过适配器模式进行数据库访问

### 主要适配器

1. **UserInfoAdapter**: 用户信息操作
2. **UserSessionAdapter**: 用户会话操作

### Elasticsearch操作

1. **ChatESOperator**: 聊天记录操作
2. **JupyterOperator**: Jupyter记录操作

## 性能优化建议

### 1. 数据库优化

- 使用连接池减少连接开销
- 批量操作提高效率
- 适当建立索引

### 2. 缓存策略

- 考虑使用Redis缓存频繁访问的数据
- 缓存用户信息和会话信息

### 3. 异步处理

- 充分利用asyncio提高并发性能
- 后台任务处理耗时操作

### 4. 日志优化

- 避免在生产环境记录过多DEBUG日志
- 使用结构化日志便于分析

## 安全考虑

### 1. 输入验证

- 使用Pydantic进行严格的参数验证
- 防止SQL注入攻击
- 输入数据长度限制

### 2. 权限控制

- 通过sub_account_uin进行用户隔离
- 会话级别的权限控制

### 3. 数据保护

- 敏感信息脱敏
- 使用HTTPS传输
- 数据库连接加密

## 部署和监控

### 1. 健康检查

- 提供健康检查接口
- 监控数据库连接状态
- 监控外部服务依赖

### 2. 性能监控

- 集成Prometheus指标
- 监控接口响应时间
- 监控错误率

### 3. 日志监控

- 集中式日志收集
- 错误日志告警
- 性能日志分析

## 维护指南

### 1. 代码维护

- 遵循PEP 8编码规范
- 添加适当的注释和文档字符串
- 定期进行代码审查

### 2. 依赖管理

- 使用requirements.txt管理依赖
- 定期更新依赖包版本
- 注意安全漏洞修复

### 3. 测试策略

- 单元测试覆盖核心逻辑
- 集成测试验证接口功能
- 性能测试确保系统稳定性

### 4. 版本控制

- 使用语义化版本号
- 维护CHANGELOG.md
- 标记重要版本发布

## 常见问题

### Q1: 如何添加新的接口？

A1: 按照现有模式，在gw.py中添加新的路由处理函数，定义相应的数据模型，实现业务逻辑。

### Q2: 如何处理数据库连接失败？

A2: 系统会自动记录错误日志，返回500状态码。需要检查数据库配置和网络连接。

### Q3: 如何优化接口性能？

A3: 可以考虑添加缓存、优化数据库查询、使用异步处理等方式提高性能。

### Q4: 如何调试接口问题？

A4: 查看详细的日志信息，包括trace_id，使用Postman等工具进行接口测试。

## 扩展性考虑

### 1. 水平扩展

- 支持多实例部署
- 使用负载均衡
- 共享存储和缓存

### 2. 功能扩展

- 模块化设计便于添加新功能
- 插件化架构支持第三方扩展
- 标准化接口便于集成

### 3. 技术升级

- 保持依赖包版本更新
- 关注FastAPI框架更新
- 适时引入新技术栈

---

**文档版本**: 1.0  
**最后更新**: 2024年  
**维护人员**: 开发团队